# 协商共和国：项目总目录

> **"在混乱中寻求秩序，在分歧中达成共识，在多元中实现统一。"**

---

## 🏗️ 项目核心文档

### 📋 总体规划与指导
- [**创作总体规划**](创作总体规划.md) ⭐ - 全书创作的统一指导文档
- [项目README](README.md) - 项目基本介绍
- [AI协作规则](CURSOR_RULES.md) - AI辅助创作的核心指导
- [项目路线图](roadmap.md) - 开发进度和里程碑

---

## 🌍 世界构建文档

### 核心设定
- [**协商共和国世界构建手册**](worldbuilding/协商共和国世界构建手册.md) ⭐ - 完整的世界观设定
- [术语词汇表](worldbuilding/术语词汇表.md) - 标准化术语定义
- [派系与机构详解](worldbuilding/派系与机构详解.md) - 政治结构说明

### 角色设定
- [角色传记](worldbuilding/角色传记.md) - 主要角色的详细背景

---

## 📖 小说手稿

### 第一部分：协商共和国编年史 (25,000-30,000字)
**主题：觉醒与初遇** | **进度：1/5章完成**

- [部分规划文档](manuscript/第一部分_协商共和国编年史/README.md)
- [✅ Chapter 1: 我不是被选出来的，而是被评分出来的](manuscript/第一部分_协商共和国编年史/第一章_我不是被选出来的而是被评分出来的.md) (3,700字)
- [🟡 Chapter 2: 五年计划进校园](manuscript/第一部分_协商共和国编年史/第二章_五年计划进校园.md) (需扩展)
- [⚪ Chapter 3: 深入NGC大楼] - 待创作
- [⚪ Chapter 4: 与RESTORE AI的对话] - 待创作
- [⚪ Chapter 5: 家庭内部的分歧] - 待创作

### 第二部分：唯一候选人 (30,000-40,000字)
**主题：适应与抉择** | **进度：规划完成，待创作**

- [部分规划文档](manuscript/第二部分_唯一候选人/README.md)
- [⚪ Chapter 6: 学习新的政治语言] - 待创作
- [⚪ Chapter 7: 参与等额选举] - 待创作
- [⚪ Chapter 8: 社区治理体验] - 待创作
- [⚪ Chapter 9: 经济转型的冲击] - 待创作
- [⚪ Chapter 10: 代际价值观冲突高潮] - 待创作

### 第三部分：系统的胜利 (25,000-35,000字)
**主题：融合与新生** | **进度：规划完成，待创作**

- [部分规划文档](manuscript/第三部分_系统的胜利/README.md)
- [⚪ Chapter 11: 新身份的建构] - 待创作
- [⚪ Chapter 12: 制度内的个人空间] - 待创作
- [⚪ Chapter 13: 最终的政治选择] - 待创作
- [⚪ Chapter 14: 家庭和解] - 待创作
- [⚪ Chapter 15: 新常态] - 待创作

---

## 🤖 AI协作资源

### 提示模板
- [场景生成模板](prompts/提示模板/场景生成.md)
- [角色对话模板](prompts/提示模板/角色对话.md)

### 创作指导
- [风格指南](resources/风格指南.md)
- [记忆存档](resources/memory.md)
- [已完成提示](prompts/已完成提示/) - 历史创作记录
- [当前章节提示](prompts/当前章节提示/) - 进行中的创作

---

## 📊 项目状态总览

### 整体进度
```
📈 总体进度: 3,957/80,000-105,000字 (约5%)

✅ 规划阶段: 100% 完成
├── 世界构建手册 ✅
├── 总体创作规划 ✅  
├── 三部分详细规划 ✅
└── AI协作体系 ✅

🔄 创作阶段: 7% 完成
├── 第一部分: 1/5章完成
├── 第二部分: 0/5章完成  
└── 第三部分: 0/5章完成
```

### 质量控制
- **世界观一致性**: ✅ 建立完善
- **角色发展弧线**: ✅ 规划完成
- **语言风格统一**: 🔄 持续监控
- **AI协作优化**: ✅ 体系建立

### 近期目标
1. **扩展Chapter 2** - 从257字扩展到6,000字
2. **创作Chapter 3** - NGC大楼场景
3. **完成第一部分** - 2025年7月底前

---

## 🛠️ 协作工具

### 版本控制
- **Git管理**: 创作历史追踪
- **分支策略**: feature分支用于各章节创作
- **Pull Request**: 质量审查流程

### 文档标准
- **Markdown格式**: 统一文档标准
- **文件命名**: 规范化命名规则
- **目录结构**: 模块化组织

### AI辅助
- **Cursor Rules**: 核心AI协作规范
- **提示模板**: 标准化创作指导
- **质量检查**: 自动化风格检查

---

## 🎯 成功指标

### 创作质量
- [ ] 完成80,000-105,000字的完整作品
- [ ] 保持"荒诞现实主义"风格一致性
- [ ] 实现角色完整的发展弧线
- [ ] 维持政治中性的叙述立场

### 世界构建
- [ ] 建立可信的未来政治制度
- [ ] 展现中美制度融合的创新可能
- [ ] 创造独特的"美式官腔"语言体系
- [ ] 构建完整的社会治理体系

### 技术创新
- [ ] 建立AI友好的创作范式
- [ ] 开发可复制的创作方法论
- [ ] 实现人机协作的最佳实践
- [ ] 产出高质量的创作工具集

---

## 📚 阅读指南

### 对于读者
1. **入门**: 从[项目README](README.md)开始了解基本概念
2. **背景**: 阅读[世界构建手册](worldbuilding/协商共和国世界构建手册.md)理解设定
3. **阅读**: 按章节顺序从第一部分开始
4. **参考**: 使用[术语词汇表](worldbuilding/术语词汇表.md)查询专业术语

### 对于创作者
1. **核心**: 必读[创作总体规划](创作总体规划.md)和[CURSOR_RULES](CURSOR_RULES.md)
2. **世界观**: 熟悉[世界构建手册](worldbuilding/协商共和国世界构建手册.md)
3. **工具**: 使用[提示模板](prompts/提示模板/)进行创作
4. **标准**: 遵循[风格指南](resources/风格指南.md)

### 对于AI协作
1. **基础**: 加载[CURSOR_RULES](CURSOR_RULES.md)作为主要指导
2. **参考**: 使用[世界构建手册](worldbuilding/协商共和国世界构建手册.md)保持设定一致
3. **模板**: 应用[提示模板](prompts/提示模板/)确保输出质量
4. **检查**: 对照[术语词汇表](worldbuilding/术语词汇表.md)保证用词准确

---

*"这不是乌托邦，也不是反乌托邦，这是一个理性选择的结果，一个在新时代背景下对美国民主的创新发展。"*

**最后更新**: 2025年7月3日  
**项目状态**: 规划完成，创作进行中