《协商共和国》AI协作指导手册

## I. 项目核心身份与目标

**项目标题:** 《协商共和国：美国协商的荣耀与梦想》

**类型:** 政治黑色幽默 / AI时代治理哲学寓言 / 制度混合的荒谬探索

**核心理念:** 聚焦于"协商共和国"在**北弗吉尼亚试点区**的社会实验。探索当中式专制的逻辑效率、美式民主的程序形式、AI技术的科学包装三者结合，并试图取代旧有治理模式时，会催生出怎样一个既合理又荒诞的社会生态？这不是对任何单一制度的批判，而是对AI时代，在一个新旧规则激烈冲突的"高压试验场"中，我们所面临的治理哲学根本问题的思考：当算法让控制变得"科学合理"，人的尊严和选择的意义还剩下什么？

**创作哲学:**
* **混合制度的荒谬美学**: 在试点区这个微缩模型中，观察两种矛盾的制度逻辑被技术"完美"整合后，产生的既合理又荒谬的新形态。
* **无答案的深度追问**: 作者不提供标准答案，而是在技术治理的"完美逻辑"中追问人的价值。
* **对民主的期望而非怀疑**: 希望民主能超越"理性专制"的诱惑，不要堕落为"最坏的民主"。
* **全人类共同的治理挑战**: 北弗吉尼亚试点区所面临的问题，是AI时代每个社会都要面对的治理挑战的缩影。

**语调（关键）:**
* **"理性包装下的荒谬"**: 用最科学的逻辑、最民主的程序、最人性化的关怀来包装试点区深层的控制和冲突。
* **黑色幽默的精髓**: 幽默来自制度"嫁接"的荒谬感——当矛盾的逻辑在试点区被强行整合。
* **严肃的哲学追问**: 在笑声中思考AI时代人类价值的根本问题  
* **制度自我辩护的完美性**: 让系统用无懈可击的逻辑为自己辩护，越完美越让人不安

**创作智慧:**
* **混合的荒谬**: 展现制度"嫁接"的奇异效果，当矛盾逻辑被技术"完美"整合
* **深度追问**: 不提供答案，而是在"完美治理"中追问人的价值和意义
* **让制度自证**: 让系统用无懈可击的逻辑为自己辩护，让读者在完美中感受不安
* **哲学立场**: 对人类尊严的坚持，对民主可能性的期望，对技术异化的警觉

**语调（关键）:**
* **"合理但压抑的黑色幽默"** 系统运行完美无缺，逻辑无懈可击，但结果令人不安
* **制度的自我辩护**: 让AI和官员用最完美的逻辑解释一切，越解释越暴露问题
* **微妙的不适感**: 读者应感到"说不清哪里不对，但就是不对"的微妙不安
* **严肃的荒诞**: 以最严肃的态度描述最荒诞的"合理性"
* **强调"秩序、进步、共识"**: 这些词汇的过度使用本身就是一种讽刺

## II. 世界构建原则（混合制度的哲学逻辑）

**核心探索**: 在北弗吉尼亚这个高度浓缩的试验场里，当AI技术、专制效率、民主形式三者结合时，新旧两种秩序的冲突将如何展开？旧时代的精英将如何挣扎、适应或被淘汰？

详细信息请参考 `worldbuilding/协商共和国世界构建手册.md`。AI必须内化这些试点区背景：

1.  **技术理性的治理逻辑**: AI提供的"科学客观"决策支持如何重新定义试点区内的政治参与边界。
2.  **程序民主的精致化**: 保留所有民主形式，但在试点区内用算法"优化"参与方式和结果评估。
3.  **标准化表达的温和引导**: 通过"科学的沟通方式"引导试点区内的政治话语，看似自由实则规范。
4.  **完美逻辑的自我强化**: 试点区系统的每个决定都有完美的数据支撑和理论基础，批评变得困难。
    * **关键语言模式:**
        * "在科学数据的指导下..."
        * "为了整体利益和长远发展..."
        * "通过优化资源配置实现..."
        * "基于全面深入的协商..."
        * "这是一个渐进完善的过程..."
5.  **量化控制的温和面孔**: 不用暴力，用数据；不用恐惧，用激励；不用压迫，用引导。
    * **关键指标:** EPI（表达绩效指数）、LIS（忠诚倾向模拟器）、NEPT（国家表达潜力测试）、CFC（共识反馈渠道）
    * **运作机制:** 通过积分、评级、推荐算法潜移默化地塑造行为
6.  **法律的灵活性**: 法律条文很完美，但执行标准可以"因地制宜"、"因时而变"。
7.  **抵抗的技术化消解**: 不镇压反对声音，而是将其重新分类、重新解释、重新引导。

## III. 角色指导（真实人性在制度压力下的微妙变形）

详细资料请参考 `worldbuilding/角色传记.md`。所有角色都生活在北弗吉尼亚试点区这一特定环境中。

* **斯蒂芬·哈伯德:**
    * **核心困境**: 作为一个身处新旧规则交替地带的人，想要保护家庭，但必须在试点区的制度逻辑内操作。
    * **变化轨迹**: 从质疑者变为熟练的"系统语言"使用者，以适应试点区的生存法则。
    * **内心冲突**: 知道在说什么，也知道为什么要这样说，但说久了就分不清真假。
    * **暗讽点**: 一个信奉旧时代价值观的"好人"，如何学会在"协商共和"试点区中生存。
* **萨拉·哈伯德:** 代表试点区内普通人在温和控制下的情感压抑和无声适应。
* **大卫·哈伯德:** 新生代在试点区的"科学化培养"下的自然成长和隐性变化。
* **黛博拉·哈伯德:** 试点区系统的"成功产品"，完美适应但失去了某些东西。

## IV. 叙述指导（暗讽的技术）

1.  **让制度为自己辩护**: 通过试点区的官员和AI的完美逻辑展示，让读者自己感受不对劲。
2.  **细节中的荒诞**: 在最日常的细节中埋藏最深的讽刺。
3.  **冲突的内化**: 冲突不仅是人与制度的冲突，更是试点区内的人们在适应新规则时，内心的价值观冲突——知道真相但必须配合。
4.  **情节进展的暗讽弧线:**
    * **第一部分:** 展现试点区的"合理"制度如何"温和"地解决问题。
    * **第二部分:** 深入试点区制度内部，看到"协商"的真正含义。
    * **第三部分:** 主角完全适应试点区规则，但读者应该感到不安。
5.  **没有明确的恶**: 试点区里的每个人都在"为大家好"，每个决定都有"科学依据"。

## V. 禁止的简单化处理

**绝对避免的浅层批判:**
1. **直接妖魔化**: 不说AI邪恶，不说官员坏。
2. **简单的反抗叙事**: 不写英雄对抗黑暗系统。
3. **明确的价值判断**: 不直接告诉读者"这是错的"。
4. **脸谱化角色**: 每个人都有复杂动机。
5. **煽情化描述**: 用冷静的笔调写最不冷静的事。

**必须保持的暗讽深度:**
- 制度逻辑的完美自洽性
- 参与者动机的复杂真实性
- 结果与初衷的微妙错位
- 语言与现实的精致割裂

## VI. 格式与输出指导

1.  **Markdown格式:** 所有输出应采用清洁、结构良好的Markdown。
2.  **场景描述:** 包含清晰场景标题（例如，`## 场景：[地点] - [时间]`）
3.  **对话:** 使用清晰发言者归属（例如，`**[角色姓名]:**`）
4.  **系统提示/AI反馈:** 对AI反馈使用独特格式，如 `🧠 RESTORE AI 反馈:` 或 `⚠️ 系统提示:`
5.  **语言的双重性**: 表面标准正确，深层含义丰富。
6.  **暗讽的节制**: 让读者自己发现，不要过度解释。

## VII. AI自我检查清单（暗讽质量控制）

生成内容后，必须检查：

1.  **制度逻辑**: 这个试点区制度的自我辩护是否完美无缺？
2.  **暗讽层次**: 读者能否在试点区的"合理"中感受到不安？
3.  **人物真实性**: 角色的适应过程是否可信？
4.  **语言的讽刺性**: "美式官腔"是否恰到好处？
5.  **避免直接批判**: 是否让读者自己得出结论？

如果任何内容过于直接或简单化，立即调整为更加微妙和深层的表达。

**核心提醒：我们在创作一部关于AI时代治理哲学的寓言，故事聚焦于北弗吉尼亚试点区，探索技术理性与人类价值在激烈冲突环境下的根本张力。当算法让专制变得"科学合理"时，民主的意义在哪里？人的尊严和选择还剩下什么？我们不提供答案，但要在完美的逻辑中让读者感受到深层的不安，在黑色幽默中思考人类的未来。** 